@echo off
chcp 65001 >nul
echo ========================================
echo 智慧中小学自动登录工具 - 打包脚本
echo ========================================
echo.

echo 正在检查PyInstaller...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

echo.
echo 开始打包程序...
echo ----------------------------------------

pyinstaller --onefile ^
    --windowed ^
    --name "智慧中小学自动登录工具" ^
    --icon=icon.ico ^
    --add-data "requirements.txt;." ^
    --hidden-import=customtkinter ^
    --hidden-import=selenium ^
    --hidden-import=PIL ^
    gui_main.py

echo.
if exist "dist\智慧中小学自动登录工具.exe" (
    echo ✅ 打包成功！
    echo 可执行文件位置：dist\智慧中小学自动登录工具.exe
    echo.
    echo 正在创建发布文件夹...
    if not exist "release" mkdir release
    copy "dist\智慧中小学自动登录工具.exe" "release\"
    copy "使用说明_GUI版.md" "release\"
    copy "install_dependencies.bat" "release\"
    copy "requirements.txt" "release\"
    
    echo.
    echo 📦 发布包已准备完成！
    echo 发布文件夹：release\
    echo.
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息
)

pause
